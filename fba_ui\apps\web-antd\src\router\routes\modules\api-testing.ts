import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:test-tube',
      keepAlive: true,
      order: 1000,
      title: 'API测试',
    },
    name: 'ApiTesting',
    path: '/api-testing',
    children: [
      {
        name: 'ApiTestingDashboard',
        path: '/api-testing/dashboard',
        component: () => import('#/views/api-testing/dashboard/index.vue'),
        meta: {
          icon: 'lucide:bar-chart-3',
          title: '测试仪表板',
        },
      },
      {
        name: 'ApiTestingProject',
        path: '/api-testing/project',
        component: () => import('#/views/api-testing/project/index.vue'),
        meta: {
          icon: 'lucide:folder',
          title: '项目管理',
        },
      },
      {
        name: 'ApiTestingTestCase',
        path: '/api-testing/test-case',
        component: () => import('#/views/api-testing/test-case/index.vue'),
        meta: {
          icon: 'lucide:file-text',
          title: '测试用例',
        },
      },
      {
        name: 'ApiTestingTestStep',
        path: '/api-testing/test-step',
        component: () => import('#/views/api-testing/test-step/index.vue'),
        meta: {
          icon: 'lucide:list',
          title: '测试步骤',
        },
      },
      {
        name: 'ApiTestingTestReport',
        path: '/api-testing/test-report',
        component: () => import('#/views/api-testing/test-report/index.vue'),
        meta: {
          icon: 'lucide:file-bar-chart',
          title: '测试报告',
        },
      },
      {
        name: 'ApiTestingTestReportDetail',
        path: '/api-testing/test-report/:id',
        component: () => import('#/views/api-testing/test-report/detail.vue'),
        meta: {
          hideInMenu: true,
          title: '报告详情',
        },
      },
    ],
  },
];

export default routes;
