2025-07-16 10:47:19.308 | INFO     | - | Shutting down
2025-07-16 10:47:19.423 | INFO     | - | Finished server process [1692]
2025-07-16 10:47:39.803 | INFO     | - | Started server process [26488]
2025-07-16 10:47:39.803 | INFO     | - | Waiting for application startup.
2025-07-16 10:47:39.961 | INFO     | - | Application startup complete.
2025-07-16 10:48:13.327 | INFO     | 313a271096e34ddaadf3f543853659c4 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 328.645ms
2025-07-16 10:48:13.366 | INFO     | a97f9973d58449809b4e406b41b4e903 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 15.218ms
2025-07-16 10:48:13.685 | INFO     | 9ee4b23e6ff34737897c5f7dda0163a5 | 127.0.0.1:13926 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 10:48:13.687 | INFO     | - | connection open
2025-07-16 10:48:18.944 | INFO     | 4c324cd6815d4cd69336f901e4e6eed7 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 14.345ms
2025-07-16 10:48:22.773 | INFO     | d0132769d7e34642bb6f0170ae804500 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/2/status | 18.850ms
2025-07-16 10:49:26.161 | INFO     | - | Shutting down
2025-07-16 10:49:26.162 | INFO     | - | connection closed
2025-07-16 10:49:26.271 | INFO     | - | Waiting for application shutdown.
2025-07-16 10:49:26.273 | INFO     | - | Application shutdown complete.
2025-07-16 10:49:26.273 | INFO     | - | Finished server process [26488]
2025-07-16 10:49:33.257 | INFO     | - | Started server process [18816]
2025-07-16 10:49:33.258 | INFO     | - | Waiting for application startup.
2025-07-16 10:49:33.299 | INFO     | - | Application startup complete.
2025-07-16 10:49:33.418 | INFO     | 5d383848966a46639dbfdbf7070931dc | 127.0.0.1:14047 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 10:49:33.419 | INFO     | - | connection open
2025-07-16 10:49:39.314 | INFO     | c3355347035c44a88617be8dd798f1b1 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/2/executions | 720.352ms
2025-07-16 10:49:54.382 | INFO     | 830e36b313c14cf4a0acca8a5f2d80c9 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/3/executions | 531.908ms
2025-07-16 10:49:57.060 | INFO     | e5fb0766e63942afba16697d220337bb | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 27.472ms
2025-07-16 10:49:58.557 | INFO     | 91b4e42ea9204b6db7aa78fe7abf86e3 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.328ms
2025-07-16 10:49:59.161 | INFO     | 98350ce18a34412fab7d9b01aad96456 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.464ms
2025-07-16 10:49:59.332 | INFO     | 7ae92826eef64b518efb8bc922495aec | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 12.487ms
2025-07-16 10:50:46.605 | INFO     | e3ec03eb1d524d9fb5c08d238243ac99 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 9.863ms
2025-07-16 10:50:47.125 | INFO     | 7970697b323f40c6bfdd30696cea7085 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.142ms
2025-07-16 10:50:56.273 | INFO     | 6111f86ed9694ce699477ba21d169c38 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/4/executions | 521.929ms
2025-07-16 10:50:59.964 | INFO     | 961d2d946888449687917b1e28c298d4 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.271ms
2025-07-16 10:51:00.155 | INFO     | 014f8913cf37448c916dd718d9905028 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.184ms
2025-07-16 10:51:00.308 | INFO     | 02f9635c2d194cedb6690a3d72e393c7 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 12.690ms
2025-07-16 10:51:00.471 | INFO     | a3c48a90c45349e999e296f5b5de8411 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 10.184ms
2025-07-16 10:51:00.637 | INFO     | f9c74504fbca43608b0c63a6920a5fd4 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 12.797ms
2025-07-16 10:51:00.811 | INFO     | 836af67f10624c60805a35fdae017922 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.733ms
2025-07-16 10:51:00.970 | INFO     | ac90d2be37c94b1aada305ef0fd9ab53 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.663ms
2025-07-16 10:51:08.537 | INFO     | e7f171b345d74d15b3201597755bf2e4 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 516.362ms
2025-07-16 10:51:09.604 | INFO     | ec670b20b56a4687a32de52b1c94fe64 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 761.530ms
2025-07-16 10:51:12.046 | INFO     | b49286eb126d4ad29fc7f285d68ad9e1 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 9.932ms
2025-07-16 10:51:12.464 | INFO     | 6b2f6b9bdae64262b2b6a4a1cb14f9b6 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.925ms
2025-07-16 10:51:31.145 | INFO     | f23bc08681b64b81acce272df666a04c | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/2/executions | 527.139ms
2025-07-16 10:51:34.144 | INFO     | c2868c1d57fd45c192ab65106da78a24 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/5/executions | 520.777ms
2025-07-16 10:51:36.987 | INFO     | f8919ac130f64d36a2a28feb40a3f27c | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 514.569ms
2025-07-16 10:51:40.910 | INFO     | 5d59925aa0674ce297f9981eb923e904 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/3/executions | 526.886ms
2025-07-16 10:51:42.090 | INFO     | 10f4e4a0f30f4ab8bdc34dcabdc2c72c | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/4/executions | 522.981ms
2025-07-16 10:51:43.182 | INFO     | 1c23a2fcce454d56bb0320ca7ae0d4e0 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/4/executions | 870.522ms
2025-07-16 10:51:43.323 | INFO     | c9e6f89a1f4a4915a09393962d34251e | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/4/executions | 524.307ms
2025-07-16 10:51:43.553 | INFO     | a4f8dd305741424db1c5b4fc17929e97 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/4/executions | 551.115ms
2025-07-16 10:54:28.564 | INFO     | - | Shutting down
2025-07-16 10:54:28.565 | INFO     | - | connection closed
2025-07-16 10:54:28.675 | INFO     | - | Waiting for application shutdown.
2025-07-16 10:54:28.676 | INFO     | - | Application shutdown complete.
2025-07-16 10:54:28.677 | INFO     | - | Finished server process [18816]
2025-07-16 10:54:33.456 | INFO     | - | Started server process [776]
2025-07-16 10:54:33.457 | INFO     | - | Waiting for application startup.
2025-07-16 10:54:33.498 | INFO     | - | Application startup complete.
2025-07-16 10:54:33.500 | INFO     | 745d73a9d2fa4da6a87264fafa016ae2 | 127.0.0.1:7624 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 10:54:33.502 | INFO     | - | connection open
2025-07-16 10:54:36.319 | INFO     | - | Shutting down
2025-07-16 10:54:36.320 | INFO     | - | connection closed
2025-07-16 10:54:36.430 | INFO     | - | Waiting for application shutdown.
2025-07-16 10:54:36.432 | INFO     | - | Application shutdown complete.
2025-07-16 10:54:36.433 | INFO     | - | Finished server process [776]
2025-07-16 10:54:42.127 | INFO     | - | Started server process [16800]
2025-07-16 10:54:42.128 | INFO     | - | Waiting for application startup.
2025-07-16 10:54:42.168 | INFO     | - | Application startup complete.
2025-07-16 10:54:42.392 | INFO     | 61a72feebf74412297eef306ea415d49 | 127.0.0.1:7659 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 10:54:42.394 | INFO     | - | connection open
2025-07-16 10:54:50.973 | INFO     | 106c9c6d4b74465bbcdb613d107fc45b | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 630.317ms
2025-07-16 10:54:54.596 | INFO     | df82857819d34f3ba4fa3f6493b7a69a | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 19.296ms
2025-07-16 10:54:54.961 | INFO     | 981ea2726ce3435abee314636a6b59fa | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.012ms
2025-07-16 10:54:55.163 | INFO     | 39cf2d1f8fe2409ea48db01bc0a27625 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.631ms
2025-07-16 10:55:04.957 | INFO     | 5d0291bfbc704c54abd5f5fb024a136b | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/2/executions | 528.793ms
2025-07-16 10:55:52.691 | INFO     | d3961153776a4e3487e771bd201f248a | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/5 | 25.538ms
2025-07-16 10:55:52.774 | INFO     | a522a84a6d8a4549ad698f3dbc2297ad | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 14.390ms
2025-07-16 10:55:54.096 | INFO     | ecb4de1b843c4526852f5338f7903825 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/5/executions | 521.270ms
2025-07-16 10:56:02.937 | INFO     | 10039cbf628b4507b07d4e63ed133f3c | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/5/executions | 525.140ms
2025-07-16 11:07:57.646 | INFO     | 92de59053d0844f08cc9e51592b3f20d | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 520.601ms
2025-07-16 11:08:13.745 | INFO     | 92a69fc6a6bc4d71aa9e8f49584f5914 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 10.063ms
2025-07-16 11:08:13.955 | INFO     | 5449a0822cc44ee19fcc7c66f22dc77b | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.729ms
2025-07-16 11:08:21.747 | INFO     | 24fb8eac552b4c1d9e6d45c3d4f331bd | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.677ms
2025-07-16 11:08:40.402 | INFO     | - | Shutting down
2025-07-16 11:08:40.403 | INFO     | - | connection closed
2025-07-16 11:08:40.512 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:08:40.513 | INFO     | - | Application shutdown complete.
2025-07-16 11:08:40.513 | INFO     | - | Finished server process [16800]
2025-07-16 11:08:45.593 | INFO     | - | Started server process [22940]
2025-07-16 11:08:45.593 | INFO     | - | Waiting for application startup.
2025-07-16 11:08:45.638 | INFO     | - | Application startup complete.
2025-07-16 11:08:46.535 | INFO     | cf354cf522914c2f85b9dd566b287732 | 127.0.0.1:14657 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:08:46.537 | INFO     | - | connection open
2025-07-16 11:08:51.980 | INFO     | fc11a83eb2a048f1b32ce926b2bd89e5 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 622.510ms
2025-07-16 11:08:54.437 | INFO     | 524e876cf97d4b8ba4e6d9df274a6b69 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 19.999ms
2025-07-16 11:08:55.021 | INFO     | e3a1395da1c34268958813ed353b947c | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 12.133ms
2025-07-16 11:09:20.823 | INFO     | - | Shutting down
2025-07-16 11:09:20.825 | INFO     | - | connection closed
2025-07-16 11:09:20.934 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:09:20.936 | INFO     | - | Application shutdown complete.
2025-07-16 11:09:20.936 | INFO     | - | Finished server process [22940]
2025-07-16 11:09:28.147 | INFO     | - | Started server process [14064]
2025-07-16 11:09:28.148 | INFO     | - | Waiting for application startup.
2025-07-16 11:09:28.205 | INFO     | - | Application startup complete.
2025-07-16 11:09:31.516 | INFO     | 82475b0091214daebba01c7d9688f3bc | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 683.275ms
2025-07-16 11:09:32.769 | INFO     | 313bac9bfaf3411782ebbaa1683d6f6e | 127.0.0.1:9103 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:09:32.770 | INFO     | - | connection open
2025-07-16 11:09:34.077 | INFO     | 3d579a020e29421ca81852cc4535579d | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 18.571ms
2025-07-16 11:09:34.249 | INFO     | 1b90a101edca4cd98a9b93c4da7c9584 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.295ms
2025-07-16 11:09:34.412 | INFO     | f636657a926c458c8d9e2cf4c862a778 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 12.789ms
2025-07-16 11:14:22.950 | INFO     | - | Shutting down
2025-07-16 11:14:22.951 | INFO     | - | connection closed
2025-07-16 11:14:23.061 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:14:23.063 | INFO     | - | Application shutdown complete.
2025-07-16 11:14:23.063 | INFO     | - | Finished server process [14064]
2025-07-16 11:14:27.366 | INFO     | - | Started server process [27492]
2025-07-16 11:14:27.367 | INFO     | - | Waiting for application startup.
2025-07-16 11:14:27.407 | INFO     | - | Application startup complete.
2025-07-16 11:14:27.416 | INFO     | d653b1d3e3cd48ac9cd22798a17702eb | 127.0.0.1:13684 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:14:27.418 | INFO     | - | connection open
2025-07-16 11:14:32.615 | INFO     | - | Started server process [23020]
2025-07-16 11:14:32.615 | INFO     | - | Waiting for application startup.
2025-07-16 11:14:32.662 | INFO     | - | Application startup complete.
2025-07-16 11:14:48.484 | INFO     | 3fdfce50c2a248e0b8500a7bc03d5cbf | 127.0.0.1:13795 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:14:48.486 | INFO     | - | connection open
2025-07-16 11:42:32.646 | INFO     | - | Shutting down
2025-07-16 11:42:32.648 | INFO     | - | connection closed
2025-07-16 11:42:32.655 | INFO     | 385db517699740a9824a0799c8cd5863 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/1/status | 87.845ms
2025-07-16 11:42:32.751 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:42:32.752 | INFO     | - | Application shutdown complete.
2025-07-16 11:42:32.753 | INFO     | - | Finished server process [23020]
2025-07-16 11:42:37.829 | INFO     | - | Started server process [27860]
2025-07-16 11:42:37.829 | INFO     | - | Waiting for application startup.
2025-07-16 11:42:37.870 | INFO     | - | Application startup complete.
2025-07-16 11:42:37.872 | INFO     | a7f389998af8498c83c2f6b2daa92ef6 | 127.0.0.1:7334 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:42:37.875 | INFO     | - | connection open
2025-07-16 11:42:54.237 | INFO     | - | Shutting down
2025-07-16 11:42:54.238 | INFO     | - | connection closed
2025-07-16 11:42:54.347 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:42:54.349 | INFO     | - | Application shutdown complete.
2025-07-16 11:42:54.349 | INFO     | - | Finished server process [27860]
2025-07-16 11:42:59.449 | INFO     | - | Started server process [22776]
2025-07-16 11:42:59.450 | INFO     | - | Waiting for application startup.
2025-07-16 11:42:59.497 | INFO     | - | Application startup complete.
2025-07-16 11:43:00.106 | INFO     | 7a2e899eff554c3f8f915779703d89b6 | 127.0.0.1:7379 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:43:00.108 | INFO     | - | connection open
2025-07-16 11:43:08.172 | INFO     | - | Shutting down
2025-07-16 11:43:08.174 | INFO     | - | connection closed
2025-07-16 11:43:08.283 | INFO     | - | Waiting for application shutdown.
2025-07-16 11:43:08.285 | INFO     | - | Application shutdown complete.
2025-07-16 11:43:08.285 | INFO     | - | Finished server process [22776]
2025-07-16 11:43:13.432 | INFO     | - | Started server process [21592]
2025-07-16 11:43:13.432 | INFO     | - | Waiting for application startup.
2025-07-16 11:43:13.474 | INFO     | - | Application startup complete.
2025-07-16 11:43:14.425 | INFO     | 4bf8923f00b840589a73eef0282bcf4a | 127.0.0.1:7412 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 11:43:14.426 | INFO     | - | connection open
2025-07-16 11:43:35.591 | INFO     | ae97b1c339014cc3ac2595417354ea99 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/1/executions | 630.337ms
2025-07-16 11:43:40.741 | INFO     | d589864861b84e738b739a2d1eb739ad | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 18.068ms
2025-07-16 11:43:40.927 | INFO     | b4991039ef8c45c2ab1b509ec22ebe2d | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.217ms
2025-07-16 11:43:41.124 | INFO     | 7600b4355e9c4f1f9a7a5010b098d4be | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 11.551ms
2025-07-16 14:36:52.583 | INFO     | 948700ba9c844c0e94508bfc08482686 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 93.396ms
2025-07-16 14:36:53.310 | INFO     | 434ca28b05434abb9c91cb99033299ec | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.245ms
2025-07-16 15:41:33.853 | INFO     | 007db4100f744e24b5447254f45227c8 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 345.527ms
2025-07-16 15:41:34.799 | INFO     | f51e2dd00216457cb5bf3ad047841b35 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 30.271ms
2025-07-16 15:43:01.232 | INFO     | deee505b86d549e19f474d1f196a802d | 127.0.0.1       | GET      | 200    | /api/v1/logs/opera/page=1&size=20 | 108.885ms
2025-07-16 15:46:05.290 | INFO     | - | connection closed
2025-07-16 16:17:58.109 | INFO     | - | Shutting down
2025-07-16 16:17:58.217 | INFO     | - | Waiting for application shutdown.
2025-07-16 16:17:58.238 | INFO     | - | Application shutdown complete.
2025-07-16 16:17:58.241 | INFO     | - | Finished server process [21592]
2025-07-16 16:18:12.763 | INFO     | - | Started server process [30732]
2025-07-16 16:18:12.763 | INFO     | - | Waiting for application startup.
2025-07-16 16:18:12.834 | INFO     | - | Application startup complete.
2025-07-16 16:18:36.732 | INFO     | 00368577dd4e4c25a8b921988f9f69c6 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 159.543ms
2025-07-16 16:18:36.785 | INFO     | 3c04d971fde04a48918257968f03be65 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 18.135ms
2025-07-16 16:18:37.112 | INFO     | f1c1154039ec4ba4bbce22c780f8f468 | 127.0.0.1:13958 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:18:37.114 | INFO     | - | connection open
2025-07-16 16:18:53.052 | INFO     | 2bae9e7e0a5a44dbbdd42950fbf750f6 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 10.661ms
2025-07-16 16:19:00.344 | INFO     | 02877183ba8d41de89a33cf24253853a | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/2/status | 18.475ms
2025-07-16 16:19:01.248 | INFO     | c84fa013cf5e4fa88ee46a93ea36e8ee | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers/2/executions | 622.024ms
2025-07-16 16:19:03.657 | INFO     | 40dc9520209d40f6a0c96eee9fd51083 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 22.977ms
2025-07-16 16:19:04.480 | INFO     | 0e8dea8e8b8147d4b5da2024330c64ae | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.797ms
2025-07-16 16:19:04.706 | INFO     | 0c230cc920504dbe9c5540e1eda0d556 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 15.141ms
2025-07-16 16:19:04.891 | INFO     | a21e011860594b38bf28799e89910d7d | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 13.101ms
2025-07-16 16:20:18.478 | INFO     | 269099f1e98a435e850a94b892e1906f | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/5 | 26.540ms
2025-07-16 16:20:18.555 | INFO     | 1980a27fbef040a09154139638e88820 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 10.294ms
2025-07-16 16:20:31.350 | INFO     | 9f430a7aee3743b1964a885142c0f96d | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/3 | 20.060ms
2025-07-16 16:20:31.424 | INFO     | 19aa4782c76042b788b253e513090d65 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.797ms
2025-07-16 16:20:32.661 | INFO     | 09339e50ede84013a08164093e8a0622 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/2/status | 13.574ms
2025-07-16 16:20:36.728 | INFO     | a6d6dd68e8334db7a693173ae723bbf2 | 127.0.0.1       | DELETE   | 200    | /api/v1/task/schedulers/1 | 17.606ms
2025-07-16 16:20:36.777 | INFO     | 527208741fe04ac995eecc14449bf48d | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.209ms
2025-07-16 16:20:38.107 | INFO     | 23a5b962bbe2470598986d3888bd6b5d | 127.0.0.1       | DELETE   | 200    | /api/v1/task/schedulers/2 | 13.999ms
2025-07-16 16:20:38.146 | INFO     | 7fb3569dfbcd42f29853fde8f01670dc | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 7.221ms
2025-07-16 16:20:39.570 | INFO     | 6e66b561808148d2a4ac5c6ae3dabe04 | 127.0.0.1       | DELETE   | 200    | /api/v1/task/schedulers/3 | 14.113ms
2025-07-16 16:20:39.610 | INFO     | f7563c814e554148bf0a232d0a8bc543 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.917ms
2025-07-16 16:20:42.108 | INFO     | c07d2119942d4d0e8d36ba3e2f6001cb | 127.0.0.1       | DELETE   | 200    | /api/v1/task/schedulers/4 | 14.594ms
2025-07-16 16:20:42.141 | INFO     | 8e93e002267941668e3e2f7091eb72ea | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.232ms
2025-07-16 16:20:43.852 | INFO     | 7533b4580d3d4bac9df03f3f6b5eda0f | 127.0.0.1       | DELETE   | 200    | /api/v1/task/schedulers/5 | 13.974ms
2025-07-16 16:20:43.880 | INFO     | 3a67134c4f914cc3b3c341a4febb7d87 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 7.535ms
2025-07-16 16:20:47.052 | INFO     | - | connection closed
2025-07-16 16:20:50.891 | INFO     | 0f7a4327149f4ba8831af50b6858a24a | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 5.201ms
2025-07-16 16:20:50.929 | INFO     | 369c099851fe4be4addb9e034c0d8f4d | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 12.657ms
2025-07-16 16:20:51.251 | INFO     | ad97ff181f2d4471b5ab22a5cdc8b2ef | 127.0.0.1:12729 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:20:51.252 | INFO     | - | connection open
2025-07-16 16:20:51.712 | INFO     | 5f5918902bec406698b1020a3bf77dc6 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 11.588ms
2025-07-16 16:22:13.636 | INFO     | ee292b4d8a154d9283204824582e22e4 | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers | 24.626ms
2025-07-16 16:22:13.723 | INFO     | 311faaafed04402f8b3afbef3cbf4b64 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 12.857ms
2025-07-16 16:23:53.972 | INFO     | fd646fc6a4a04954ac74a4fb5cddb9f7 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 13.414ms
2025-07-16 16:23:54.062 | INFO     | 8dcaed4ed92345a99605c07ad4f0e313 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 12.951ms
2025-07-16 16:23:56.296 | INFO     | 1e7e6e016b1a489aa0b3980e769e4423 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 14.352ms
2025-07-16 16:24:59.096 | INFO     | ce7d540fc1024447b52310470ba0de0c | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 14.636ms
2025-07-16 16:24:59.408 | INFO     | 772ca38596f54616a1f8a05d0ca1a2ec | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 16.880ms
2025-07-16 16:25:36.005 | INFO     | 9f6b01cf7194482bb6356bfd80513f31 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 24.869ms
2025-07-16 16:25:36.106 | INFO     | eddf99f074be4bcba2576d507fb5ace6 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 10.218ms
2025-07-16 16:25:39.816 | INFO     | 56ddac0f2fe84819b010e5488f71e1c2 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 13.060ms
2025-07-16 16:25:40.202 | INFO     | b27260d1a76f499bb98c611b1b182483 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 14.487ms
2025-07-16 16:26:20.423 | INFO     | 8a7da4643b7a4a969d6ac9f965c827a7 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 16.026ms
2025-07-16 16:26:20.500 | INFO     | 9b1c47aeb7e241fda14f689fa3662fa0 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 12.600ms
2025-07-16 16:26:23.875 | INFO     | a9d5e7a80bf14ee3b6ec84f9940e6a3e | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 16.563ms
2025-07-16 16:26:24.478 | INFO     | 0ea35eb974464f63b0e108f44c40ef44 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 15.449ms
2025-07-16 16:26:27.236 | INFO     | 9f6451a74ff84ae6b31841c553583e89 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 22.937ms
2025-07-16 16:26:27.310 | INFO     | 34452589632d4ae5bf1ca2fd6511320f | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.862ms
2025-07-16 16:26:28.594 | INFO     | 37d885014fc74ccba99917e9802bda05 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 13.618ms
2025-07-16 16:26:40.014 | INFO     | f17176847275435881308699fd8f77f3 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 15.853ms
2025-07-16 16:26:40.091 | INFO     | 481970c5cd244c3394261716e266eed5 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 8.046ms
2025-07-16 16:27:49.632 | INFO     | 743cb2b6fc084f87af92091cfa8654f5 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 9.381ms
2025-07-16 16:31:19.031 | INFO     | - | Shutting down
2025-07-16 16:31:19.033 | INFO     | - | connection closed
2025-07-16 16:31:19.143 | INFO     | - | Waiting for application shutdown.
2025-07-16 16:31:19.145 | INFO     | - | Application shutdown complete.
2025-07-16 16:31:19.145 | INFO     | - | Finished server process [30732]
2025-07-16 16:31:24.756 | INFO     | - | Started server process [29568]
2025-07-16 16:31:24.756 | INFO     | - | Waiting for application startup.
2025-07-16 16:31:24.798 | INFO     | - | Application startup complete.
2025-07-16 16:31:25.357 | INFO     | 5f10d4770db2492a881b99b2ab0618e2 | 127.0.0.1:14960 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:31:25.359 | INFO     | - | connection open
2025-07-16 16:31:32.655 | INFO     | 6a698a40196b4b46af0431faf0794ed8 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 80.121ms
2025-07-16 16:31:32.741 | INFO     | c35972d402434974b053cc8a8f9989ba | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 15.194ms
2025-07-16 16:31:35.445 | INFO     | ef52731a4d3243859c9cebcb1627db99 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 13.023ms
2025-07-16 16:31:35.963 | INFO     | 3910b9816733472f84e9c24eb3f20a2d | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 13.185ms
2025-07-16 16:31:39.815 | INFO     | 4bacbb9088594a3db010c38b8c69186a | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 21.939ms
2025-07-16 16:31:41.608 | INFO     | fd2378a92602454bab3787d54774c65e | 127.0.0.1       | GET      | 200    | /api/v1/logs/opera/page=1&size=20 | 17.490ms
2025-07-16 16:31:50.818 | INFO     | 87a5ad3be2e34f0da409e709d6c27d87 | 127.0.0.1       | GET      | 200    | /api/v1/logs/opera/page=1&size=20 | 15.468ms
2025-07-16 16:31:56.163 | INFO     | b6389e4b5374441e8229b8003d3f3218 | 127.0.0.1       | GET      | 200    | /api/v1/monitors/sessions | 7.404ms
2025-07-16 16:31:58.409 | INFO     | 0930a8d341634753875933a19142db6a | 127.0.0.1       | GET      | 200    | /api/v1/monitors/redis | 11.616ms
2025-07-16 16:32:20.883 | INFO     | d6308d0d2a744f7d8dbf732f1523f785 | 127.0.0.1       | GET      | 200    | /api/v1/monitors/server | 20928.681ms
2025-07-16 16:32:48.504 | INFO     | f5cc74c542a74cdfb7172803d34a1285 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 121.839ms
2025-07-16 16:32:48.828 | INFO     | 9d8238ee73ff4479ba34ac0c27275f6f | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6/status | 16.082ms
2025-07-16 16:33:42.770 | INFO     | abe0d512740447d7ad2bb8477e55f2a0 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/6 | 21.425ms
2025-07-16 16:33:42.842 | INFO     | 37cb8a479b494e818220de8775a1eb8e | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 11.358ms
2025-07-16 16:34:29.811 | INFO     | 538da8030e0e48129844cd939030baa4 | 127.0.0.1       | POST     | 401    | /api/v1/task/schedulers | 0.659ms
2025-07-16 16:34:30.140 | INFO     | d35b6659e4c447d1819d0540f3176046 | 127.0.0.1       | POST     | 400    | /api/v1/auth/tokens | 5.776ms
2025-07-16 16:34:30.169 | INFO     | a629ce4714d14b50b4016536cf44da2a | 127.0.0.1       | POST     | 200    | /api/v1/auth/logout | 4.126ms
2025-07-16 16:34:30.249 | INFO     | - | connection closed
2025-07-16 16:34:30.882 | INFO     | e51fd69298234b8d86aa3b387f6bdd4e | 127.0.0.1       | GET      | 200    | /api/v1/auth/captcha | 315.636ms
2025-07-16 16:34:35.480 | INFO     | 836e5b7c27624e87ae864764a10461cf | 127.0.0.1       | POST     | 200    | /api/v1/auth/login | 308.535ms
2025-07-16 16:34:35.497 | INFO     | 7ee4ced51d54420c8aac3bec5310b37b | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 5.979ms
2025-07-16 16:34:35.514 | INFO     | 87ef027fe40e422aafbc50d67c6957a3 | 127.0.0.1       | GET      | 200    | /api/v1/auth/codes | 14.608ms
2025-07-16 16:34:35.572 | INFO     | aa888f1132e248be8547a4d8cb12178b | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 11.148ms
2025-07-16 16:34:35.857 | INFO     | 7929cfc2264d45538cb06434619e8c6d | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 13.260ms
2025-07-16 16:34:35.904 | INFO     | 79d4c183286241fd815e9e38ca601fca | 127.0.0.1:5688 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:34:35.905 | INFO     | - | connection open
2025-07-16 16:34:43.669 | INFO     | a6bb943355cc473babdb9fb12cf6e20f | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers | 27.230ms
2025-07-16 16:34:43.737 | INFO     | 2488e20660ad4d2dbc44dd3b6051f17f | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 11.012ms
2025-07-16 16:36:18.160 | INFO     | - | Shutting down
2025-07-16 16:36:18.162 | INFO     | - | connection closed
2025-07-16 16:36:18.270 | INFO     | - | Waiting for application shutdown.
2025-07-16 16:36:18.272 | INFO     | - | Application shutdown complete.
2025-07-16 16:36:18.273 | INFO     | - | Finished server process [29568]
2025-07-16 16:36:23.358 | INFO     | - | Started server process [31364]
2025-07-16 16:36:23.358 | INFO     | - | Waiting for application startup.
2025-07-16 16:36:23.973 | INFO     | - | Application startup complete.
2025-07-16 16:36:27.594 | INFO     | 6a4586c5bc924bcbbd766ff62044270f | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 69.274ms
2025-07-16 16:36:27.633 | INFO     | 94fe197319004c80bde3a633805b4a25 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 12.139ms
2025-07-16 16:36:27.947 | INFO     | 2ebf14bb927f4827aef8dd7dd3c63d2f | 127.0.0.1:5797 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:36:27.949 | INFO     | - | connection open
2025-07-16 16:37:01.253 | INFO     | - | connection closed
2025-07-16 16:37:03.147 | INFO     | b39fd54830f342a794acf53cb4c2ed96 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 15.718ms
2025-07-16 16:37:03.205 | INFO     | 3d430e84a2964f4f81fecdafa1cfb64d | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 9.940ms
2025-07-16 16:37:03.537 | INFO     | e18b93ca6d924973906f0eab9712e744 | 127.0.0.1:14081 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:37:03.538 | INFO     | - | connection open
2025-07-16 16:37:07.924 | INFO     | bb159748a1b84ed78486301686ee1867 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 14.044ms
2025-07-16 16:41:44.305 | INFO     | - | Shutting down
2025-07-16 16:41:44.306 | INFO     | - | connection closed
2025-07-16 16:41:44.401 | INFO     | - | Waiting for application shutdown.
2025-07-16 16:41:44.402 | INFO     | - | Application shutdown complete.
2025-07-16 16:41:44.403 | INFO     | - | Finished server process [31364]
2025-07-16 16:41:49.092 | INFO     | - | Started server process [10788]
2025-07-16 16:41:49.092 | INFO     | - | Waiting for application startup.
2025-07-16 16:41:49.734 | INFO     | - | Application startup complete.
2025-07-16 16:41:50.987 | INFO     | 4c4b2314e62f4bec8612eb44645bfc13 | 127.0.0.1:3354 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:41:50.988 | INFO     | - | connection open
2025-07-16 16:42:31.280 | INFO     | - | connection closed
2025-07-16 16:42:34.093 | INFO     | 1f8cb429d63c48a2858832b6c8ed6bc5 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 60.401ms
2025-07-16 16:42:34.128 | INFO     | 44a171b0486b40379a466b79723f0881 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 12.207ms
2025-07-16 16:42:34.445 | INFO     | 72a68fb3fc9044ea9b35b928bde80144 | 127.0.0.1:3413 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:42:34.447 | INFO     | - | connection open
2025-07-16 16:42:34.733 | INFO     | 1c3a87a48d6c4a12b838454b7476e9f7 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 12.283ms
2025-07-16 16:44:25.523 | INFO     | - | connection closed
2025-07-16 16:44:27.765 | INFO     | 3ed5ada6e4864fc8bd6bb4508ee7e1f5 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 5.932ms
2025-07-16 16:44:27.794 | INFO     | 7d6bebff907240018a9aaa3c2723e2ef | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 11.766ms
2025-07-16 16:44:28.108 | INFO     | be8fb3740be24f57bd6194e609373453 | 127.0.0.1:3579 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:44:28.110 | INFO     | - | connection open
2025-07-16 16:44:28.292 | INFO     | 89dea789d2e441e4950971b0a839d83c | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 13.755ms
2025-07-16 16:54:20.342 | INFO     | - | Shutting down
2025-07-16 16:54:20.343 | INFO     | - | connection closed
2025-07-16 16:54:20.453 | INFO     | - | Waiting for application shutdown.
2025-07-16 16:54:20.454 | INFO     | - | Application shutdown complete.
2025-07-16 16:54:20.455 | INFO     | - | Finished server process [10788]
2025-07-16 16:54:25.112 | INFO     | - | Started server process [27248]
2025-07-16 16:54:25.113 | INFO     | - | Waiting for application startup.
2025-07-16 16:54:25.712 | INFO     | - | Application startup complete.
2025-07-16 16:54:26.988 | INFO     | b85780eef7414f8599182185d91ec6c7 | 127.0.0.1:4242 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 16:54:26.990 | INFO     | - | connection open
2025-07-16 16:56:39.912 | INFO     | f31026dc15c446d68ed8ccf4d3ab153e | 127.0.0.1       | POST     | 200    | /api/v1/task/schedulers | 78.795ms
2025-07-16 16:56:39.996 | INFO     | 495349c0a9ce4f86aace30f65c982c01 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 15.552ms
2025-07-16 17:02:17.362 | INFO     | - | connection closed
2025-07-16 17:02:20.473 | INFO     | d6f47458dcc74086b4d05975c3f32d36 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 7.799ms
2025-07-16 17:02:20.514 | INFO     | c8615b40a896430e86bf8b31d3aeec42 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 13.211ms
2025-07-16 17:02:20.832 | INFO     | fb017ae6d54c458b8b1f3b817a1690f4 | 127.0.0.1:6832 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 17:02:20.833 | INFO     | - | connection open
2025-07-16 17:02:21.431 | INFO     | 2e234fa291ec410b9cd4b87d6e6a3213 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 17.835ms
2025-07-16 17:02:23.409 | INFO     | 08e932b0197f4355ab56ba1dfaad63c6 | 127.0.0.1       | GET      | 200    | /api/v1/task/schedulers/all | 12.060ms
2025-07-16 17:02:39.437 | INFO     | c7dd4815c1d946278c0f9320dacca890 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/2/status | 15.161ms
2025-07-16 17:02:40.203 | INFO     | 885e8d9440f849bf9456b5a2bc780435 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/1/status | 12.543ms
2025-07-16 17:02:41.400 | INFO     | 1f737fbec44747b9a6e3c881ac923ecb | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/3/status | 16.108ms
2025-07-16 17:02:42.951 | INFO     | cc1ccfb0c5cf4ba3bdc850e9274af584 | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/4/status | 13.562ms
2025-07-16 17:02:43.886 | INFO     | 6bd38073438043bf8b38dd716f93f56a | 127.0.0.1       | PUT      | 200    | /api/v1/task/schedulers/5/status | 18.429ms
2025-07-16 18:00:26.357 | INFO     | 40ec96d0f4714f918b50256022445e14 | 127.0.0.1       | GET      | 200    | /api/v1/task/results/page=1&size=20 | 26.619ms
2025-07-16 18:00:28.950 | INFO     | e69e8cfcaba54cac91e9d125edc10efc | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 19.638ms
2025-07-16 18:00:30.244 | INFO     | 7ccd775e68194ada80e403abd5feae6c | 127.0.0.1       | GET      | 200    | /api/v1/logs/opera/page=1&size=20 | 62.855ms
2025-07-16 18:02:12.948 | INFO     | 4c3f53de0b33409c8844df35d54f1300 | 127.0.0.1       | GET      | 200    | /api/v1/sys/plugins/changed | 12.206ms
2025-07-16 18:02:12.957 | INFO     | 67360644669341bda79cca258a0b5522 | 127.0.0.1       | GET      | 200    | /api/v1/sys/plugins | 21.377ms
2025-07-16 18:02:35.616 | INFO     | 6298a6105e264366946c8eab0dfea09a | 127.0.0.1       | GET      | 200    | /api/v1/sys/data-scopes/page=1&size=20 | 25.149ms
2025-07-16 18:02:36.911 | INFO     | 7f5d27a2a8d7443f89da2f6dd50aafff | 127.0.0.1       | GET      | 200    | /api/v1/sys/data-rules/page=1&size=20 | 16.955ms
2025-07-16 18:02:41.608 | INFO     | d5a97bcb645c444a801752a6a17b86ad | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 13.665ms
2025-07-16 18:02:42.226 | INFO     | 650db6f3078d4b6fa1e3f7fab74271c2 | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 12.600ms
2025-07-16 18:02:42.361 | INFO     | b904cd50b727454484003c2d88c16d7b | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 12.361ms
2025-07-16 18:02:42.584 | INFO     | 9bb8b8b91c204c2b93a9b0d798de782c | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 12.833ms
2025-07-16 18:02:49.958 | INFO     | c5b3605649ee4a7c8e66134f9b9b92f7 | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 13.044ms
2025-07-16 18:03:00.801 | INFO     | aa28214f770e49aea43347a42a3c0741 | 127.0.0.1       | GET      | 200    | /api/v1/monitors/sessions | 10.568ms
2025-07-16 18:05:43.360 | INFO     | 3842b61c0c354b39a6004c13aadd8451 | 127.0.0.1       | POST     | 200    | /api/v1/auth/logout | 9.895ms
2025-07-16 18:05:43.445 | INFO     | - | connection closed
2025-07-16 18:05:44.112 | INFO     | 560f4b76bb46428386f285dc2b47d2a1 | 127.0.0.1       | GET      | 200    | /api/v1/auth/captcha | 346.985ms
2025-07-16 18:05:47.106 | INFO     | d5bf460f53524444bd9cf86b03f35919 | 127.0.0.1       | POST     | 200    | /api/v1/auth/login | 340.706ms
2025-07-16 18:05:47.127 | INFO     | 2f888be5407e42579600c6e8496defe2 | 127.0.0.1       | GET      | 200    | /api/v1/sys/users/me | 8.378ms
2025-07-16 18:05:47.139 | INFO     | b8586162dfad456c8629d220c8d44674 | 127.0.0.1       | GET      | 200    | /api/v1/auth/codes | 20.465ms
2025-07-16 18:05:47.161 | INFO     | 57e5a02b09084295ab0e3d153c429735 | 127.0.0.1       | GET      | 200    | /api/v1/sys/menus/sidebar | 10.643ms
2025-07-16 18:05:47.478 | INFO     | df430acca24941a3a380ce4eb984b248 | 127.0.0.1:13155 - "WebSocket /ws/socket.io/?EIO=4&transport=websocket" [accepted]
2025-07-16 18:05:47.479 | INFO     | - | connection open
2025-07-16 18:05:51.844 | INFO     | 888e8cfb7cac47cc805151e169c3de08 | 127.0.0.1       | GET      | 200    | /api/v1/logs/login/page=1&size=20 | 19.136ms
