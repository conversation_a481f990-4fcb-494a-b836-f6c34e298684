[program:fastapi_server]
directory=/fba/backend
command=/usr/local/bin/granian main:app --interface asgi --host 0.0.0.0 --port 8001 --workers 1 --backlog 1024 --workers-kill-timeout 120 --backpressure 2000 --pid-file /var/run/granian.pid --log --log-level debug
user=root
autostart=true
autorestart=true
startretries=5
redirect_stderr=true
stdout_logfile=/var/log/fastapi_server/fba_server.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
