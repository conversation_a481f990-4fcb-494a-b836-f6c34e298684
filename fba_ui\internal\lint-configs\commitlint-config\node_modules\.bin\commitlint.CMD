@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules\@commitlint\cli\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules\@commitlint\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules\@commitlint\cli\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules\@commitlint\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\@commitlint+cli@19.8.1_@types+node@24.3.0_typescript@5.9.2\node_modules;F:\gitpush\fastapi_best_architecture_ui\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@commitlint\cli\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@commitlint\cli\cli.js" %*
)
