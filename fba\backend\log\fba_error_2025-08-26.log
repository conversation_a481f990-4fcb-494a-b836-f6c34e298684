2025-08-25 17:34:23.909 | ERROR    | c73b1ad1de224f3d96a325dbedc06509 | 请求异常: ResponseBase.fail() got an unexpected keyword argument 'msg'
2025-08-25 17:34:23.911 | ERROR    | c73b1ad1de224f3d96a325dbedc06509 | Exception in ASGI application

2025-08-25 18:05:05.391 | ERROR    | 9059eec098f54cb9ba60522726c70697 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="SQL执行失败: 'Settings' object has no attribute 'DB_ENGINE'")}

2025-08-25 18:05:05.394 | ERROR    | 9059eec098f54cb9ba60522726c70697 | Exception in ASGI application

2025-08-25 18:06:52.740 | ERROR    | a7c3da9c592c4352b53d391cafec92ca | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="SQL执行失败: 'Settings' object has no attribute 'DB_ENGINE'")}

2025-08-25 18:06:52.741 | ERROR    | a7c3da9c592c4352b53d391cafec92ca | Exception in ASGI application

2025-08-25 18:08:53.023 | ERROR    | 5602db622e334e7e93bbd4c7cd1aaa13 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="SQL执行失败: 'Settings' object has no attribute 'DB_HOST'")}

2025-08-25 18:08:53.024 | ERROR    | 5602db622e334e7e93bbd4c7cd1aaa13 | Exception in ASGI application

2025-08-25 18:09:58.041 | ERROR    | 9f422207dbc04f08a68d06b4804e47f6 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-25 18:09:58.043 | ERROR    | 9f422207dbc04f08a68d06b4804e47f6 | Exception in ASGI application

2025-08-25 18:10:24.982 | ERROR    | d9e010f4125743c98a1d9a3771b4a4f3 | SQL执行异常: (1065, 'Query was empty')
2025-08-25 18:10:24.982 | ERROR    | d9e010f4125743c98a1d9a3771b4a4f3 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': '', 'success': False, 'error': "SQL执行异常: (1065, 'Query was empty')", 'data': None, 'affected_rows': None, 'extracted_variables': None})}

2025-08-25 18:10:24.984 | ERROR    | d9e010f4125743c98a1d9a3771b4a4f3 | Exception in ASGI application

2025-08-25 18:10:35.154 | ERROR    | 5f5e25a161344ca4b15a3959ea2ae548 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-25 18:10:35.155 | ERROR    | 5f5e25a161344ca4b15a3959ea2ae548 | Exception in ASGI application

2025-08-25 18:13:46.210 | ERROR    | 3b140f80303c4a27b96774375188bcfd | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-25 18:13:46.212 | ERROR    | 3b140f80303c4a27b96774375188bcfd | Exception in ASGI application

2025-08-25 18:14:10.999 | ERROR    | 4d56151acf954a039d2d0aef29bddadf | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-25 18:14:11.001 | ERROR    | 4d56151acf954a039d2d0aef29bddadf | Exception in ASGI application

2025-08-25 18:15:02.954 | ERROR    | b6a4b2b038f14fb088974c93202aca54 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-25 18:15:02.955 | ERROR    | b6a4b2b038f14fb088974c93202aca54 | Exception in ASGI application

