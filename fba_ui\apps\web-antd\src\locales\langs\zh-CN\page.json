{"auth": {"login": "登录", "register": "注册", "codeLogin": "验证码登录", "qrcodeLogin": "二维码登录", "forgetPassword": "忘记密码", "captchaPlaceholder": "请输入验证码", "captchaRequired": "验证码是必须的"}, "dashboard": {"title": "概览", "analytics": "分析页", "workspace": "工作台"}, "menu": {"system": "系统管理", "scheduler": "任务调度", "schedulerManage": "任务管理", "schedulerRecord": "执行记录", "log": "日志管理", "login": "登录日志", "monitor": "系统监控", "opera": "操作日志", "online": "在线用户", "redis": "Redis", "server": "服务器", "sysDataPermission": "数据权限", "sysDataScope": "数据范围", "sysDataRule": "数据规则", "sysPlugin": "插件管理", "sysDept": "部门管理", "sysMenu": "菜单管理", "sysRole": "角色管理", "sysUser": "用户管理", "profile": "个人中心"}, "monitor": {"redis": {"cards": {"commands": {"title": "命令统计"}, "memory": {"title": "内存情况"}}, "desc": {"title": "基本信息"}, "info": {"arch": "架构", "clients": "连接数", "commands_processed": "已执行命令", "connections_received": "可接受连接数", "keys_command_stats": "查询次数", "keys_num": "Keys 数量", "memory_human": "已分配内存", "mode": "模式", "os": "操作系统", "rejected_connections": "已拒绝连接", "role": "角色", "title": "基础信息", "uptime": "运行时间", "used_cpu": "CPU 消耗", "used_cpu_children": "后台 CPU 占用", "version": "版本"}, "stats": {"title": {"used_memory": "已使用内存"}}}, "server": {"cpu": {"current_freq": "当前频率", "logical_num": "逻辑核心数", "physical_num": "物理核心数", "usage": "使用率"}, "disk": {"device": "设备", "dir": "路径", "free": "空闲", "title": "磁盘", "total": "总计", "type": "类型", "usage": "使用率", "used": "已使用"}, "memory": {"free": "空闲", "title": "内存", "total": "总量", "usage": "使用率", "used": "已使用"}, "service": "服务", "system": "系统"}}}