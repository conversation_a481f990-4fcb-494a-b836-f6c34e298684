<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { Page, VbenButton } from '@vben/common-ui';
import { Plus, Play, FileText, BarChart3 } from '@vben/icons';

import { Card, Statistic, Row, Col, List, Progress, Empty } from 'ant-design-vue';
import { message } from 'ant-design-vue';

import {
  getApiProjectListApi,
  getTestCaseListApi,
  getTestReportListApi,
} from '#/api/api-testing';

defineOptions({
  name: 'ApiTestingDashboard',
});

const router = useRouter();

// 统计数据
const stats = ref({
  totalProjects: 0,
  totalTestCases: 0,
  totalReports: 0,
  successRate: 0,
});

// 最近的项目
const recentProjects = ref([]);

// 最近的测试用例
const recentTestCases = ref([]);

// 最近的测试报告
const recentReports = ref([]);

const loading = ref(false);

// 获取统计数据
async function fetchStats() {
  loading.value = true;
  try {
    // 获取项目统计
    const projectsResult = await getApiProjectListApi({ page: 1, size: 1 });
    stats.value.totalProjects = projectsResult.total;

    // 获取测试用例统计
    const casesResult = await getTestCaseListApi({ page: 1, size: 1 });
    stats.value.totalTestCases = casesResult.total;

    // 获取测试报告统计
    const reportsResult = await getTestReportListApi({ page: 1, size: 1 });
    stats.value.totalReports = reportsResult.total;

    // 计算成功率（基于最近的报告）
    if (reportsResult.total > 0) {
      const allReports = await getTestReportListApi({ page: 1, size: 100 });
      const successCount = allReports.items.filter(report => report.success).length;
      stats.value.successRate = Math.round((successCount / allReports.items.length) * 100);
    }
  } catch (error) {
    message.error('获取统计数据失败');
  } finally {
    loading.value = false;
  }
}

// 获取最近数据
async function fetchRecentData() {
  try {
    // 获取最近的项目
    const projectsResult = await getApiProjectListApi({ page: 1, size: 5 });
    recentProjects.value = projectsResult.items;

    // 获取最近的测试用例
    const casesResult = await getTestCaseListApi({ page: 1, size: 5 });
    recentTestCases.value = casesResult.items;

    // 获取最近的测试报告
    const reportsResult = await getTestReportListApi({ page: 1, size: 5 });
    recentReports.value = reportsResult.items;
  } catch (error) {
    message.error('获取最近数据失败');
  }
}

// 快速操作
function handleQuickAction(action: string) {
  switch (action) {
    case 'create-project':
      router.push('/api-testing/project');
      break;
    case 'create-case':
      router.push('/api-testing/test-case');
      break;
    case 'view-reports':
      router.push('/api-testing/test-report');
      break;
  }
}

// 跳转到详情
function goToDetail(type: string, id: number) {
  switch (type) {
    case 'project':
      router.push('/api-testing/project');
      break;
    case 'case':
      router.push({
        name: 'ApiTestingTestStep',
        query: { test_case_id: id },
      });
      break;
    case 'report':
      router.push({
        name: 'ApiTestingTestReportDetail',
        params: { id },
      });
      break;
  }
}

onMounted(() => {
  fetchStats();
  fetchRecentData();
});
</script>

<template>
  <Page auto-content-height>
    <div class="space-y-6">
      <!-- 欢迎信息 -->
      <Card>
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold mb-2">API测试管理</h1>
            <p class="text-gray-600">管理您的API测试项目、用例和报告</p>
          </div>
          <div class="flex space-x-2">
            <VbenButton type="primary" @click="handleQuickAction('create-project')">
              <Plus class="size-4 mr-1" />
              创建项目
            </VbenButton>
            <VbenButton @click="handleQuickAction('create-case')">
              <Plus class="size-4 mr-1" />
              创建用例
            </VbenButton>
          </div>
        </div>
      </Card>

      <!-- 统计卡片 -->
      <Row :gutter="16">
        <Col :span="6">
          <Card>
            <Statistic
              title="总项目数"
              :value="stats.totalProjects"
              :loading="loading"
            >
              <template #prefix>
                <FileText class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="总测试用例"
              :value="stats.totalTestCases"
              :loading="loading"
            >
              <template #prefix>
                <Play class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="总测试报告"
              :value="stats.totalReports"
              :loading="loading"
            >
              <template #prefix>
                <BarChart3 class="size-4" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col :span="6">
          <Card>
            <Statistic
              title="平均成功率"
              :value="stats.successRate"
              suffix="%"
              :loading="loading"
              :value-style="{ 
                color: stats.successRate >= 80 ? '#3f8600' : 
                       stats.successRate >= 60 ? '#faad14' : '#cf1322' 
              }"
            >
              <template #prefix>
                <Progress
                  type="circle"
                  :percent="stats.successRate"
                  :width="20"
                  :stroke-width="8"
                  :show-info="false"
                />
              </template>
            </Statistic>
          </Card>
        </Col>
      </Row>

      <!-- 最近数据 -->
      <Row :gutter="16">
        <!-- 最近项目 -->
        <Col :span="8">
          <Card title="最近项目" :body-style="{ padding: 0 }">
            <List v-if="recentProjects.length > 0" :data-source="recentProjects">
              <template #renderItem="{ item }">
                <List.Item class="cursor-pointer hover:bg-gray-50 px-4" @click="goToDetail('project', item.id)">
                  <List.Item.Meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.description || '暂无描述' }}</template>
                  </List.Item.Meta>
                  <div class="text-sm text-gray-500">
                    {{ new Date(item.updated_time).toLocaleDateString() }}
                  </div>
                </List.Item>
              </template>
            </List>
            <Empty v-else description="暂无项目" />
          </Card>
        </Col>

        <!-- 最近测试用例 -->
        <Col :span="8">
          <Card title="最近测试用例" :body-style="{ padding: 0 }">
            <List v-if="recentTestCases.length > 0" :data-source="recentTestCases">
              <template #renderItem="{ item }">
                <List.Item class="cursor-pointer hover:bg-gray-50 px-4" @click="goToDetail('case', item.id)">
                  <List.Item.Meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.project?.name || '未知项目' }}</template>
                  </List.Item.Meta>
                  <div class="text-sm text-gray-500">
                    {{ new Date(item.update_time).toLocaleDateString() }}
                  </div>
                </List.Item>
              </template>
            </List>
            <Empty v-else description="暂无测试用例" />
          </Card>
        </Col>

        <!-- 最近测试报告 -->
        <Col :span="8">
          <Card title="最近测试报告" :body-style="{ padding: 0 }">
            <List v-if="recentReports.length > 0" :data-source="recentReports">
              <template #renderItem="{ item }">
                <List.Item class="cursor-pointer hover:bg-gray-50 px-4" @click="goToDetail('report', item.id)">
                  <List.Item.Meta>
                    <template #title>
                      <div class="flex items-center space-x-2">
                        <span>{{ item.name }}</span>
                        <div
                          class="w-2 h-2 rounded-full"
                          :class="item.success ? 'bg-green-500' : 'bg-red-500'"
                        ></div>
                      </div>
                    </template>
                    <template #description>
                      成功率: {{ Math.round((item.success_steps / item.total_steps) * 100) }}%
                    </template>
                  </List.Item.Meta>
                  <div class="text-sm text-gray-500">
                    {{ new Date(item.create_time).toLocaleDateString() }}
                  </div>
                </List.Item>
              </template>
            </List>
            <Empty v-else description="暂无测试报告" />
          </Card>
        </Col>
      </Row>

      <!-- 快速操作 -->
      <Card title="快速操作">
        <div class="grid grid-cols-3 gap-4">
          <div
            class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 hover:shadow-md transition-all"
            @click="handleQuickAction('create-project')"
          >
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Plus class="size-5 text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium">创建新项目</h3>
                <p class="text-sm text-gray-500">开始一个新的API测试项目</p>
              </div>
            </div>
          </div>

          <div
            class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-green-500 hover:shadow-md transition-all"
            @click="handleQuickAction('create-case')"
          >
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Play class="size-5 text-green-600" />
              </div>
              <div>
                <h3 class="font-medium">创建测试用例</h3>
                <p class="text-sm text-gray-500">为项目添加新的测试用例</p>
              </div>
            </div>
          </div>

          <div
            class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-purple-500 hover:shadow-md transition-all"
            @click="handleQuickAction('view-reports')"
          >
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 class="size-5 text-purple-600" />
              </div>
              <div>
                <h3 class="font-medium">查看测试报告</h3>
                <p class="text-sm text-gray-500">查看和分析测试结果</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  </Page>
</template>
