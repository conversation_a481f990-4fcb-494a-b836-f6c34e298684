<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ApiProject } from '#/api/api-testing';

import { ref } from 'vue';

import { Page, useVbenModal, VbenButton } from '@vben/common-ui';
import { MaterialSymbolsAdd } from '@vben/icons';
import { $t } from '@vben/locales';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  createApiProjectApi,
  deleteApiProjectApi,
  getApiProjectListApi,
  updateApiProjectApi,
} from '#/api/api-testing';

import { projectFormSchema, querySchema, useColumns } from './data';

defineOptions({
  name: 'ApiTestingProject',
});

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: $t('common.form.query'),
  },
  schema: querySchema,
};

// 表格配置
const gridOptions: VxeTableGridOptions<ApiProject> = {
  rowConfig: {
    keyField: 'id',
  },
  checkboxConfig: {
    highlight: true,
  },
  height: 'auto',
  exportConfig: {},
  printConfig: {},
  toolbarConfig: {
    export: true,
    print: true,
    refresh: { code: 'query' },
    custom: true,
    zoom: true,
  },
  columns: useColumns(onActionClick),
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getApiProjectListApi({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 创建/编辑项目表单
const [ProjectForm, projectFormApi] = useVbenForm({
  schema: projectFormSchema,
});

// 创建项目模态框
const [CreateModal, createModalApi] = useVbenModal({
  title: '创建项目',
  width: 600,
  onConfirm: async () => {
    const values = await projectFormApi.validate();
    if (values) {
      await createApiProjectApi(values);
      message.success('项目创建成功');
      onRefresh();
      return true;
    }
    return false;
  },
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      projectFormApi.resetForm();
    }
  },
});

// 编辑项目模态框
const editingProjectId = ref<number | null>(null);
const [EditModal, editModalApi] = useVbenModal({
  title: '编辑项目',
  width: 600,
  onConfirm: async () => {
    const values = await projectFormApi.validate();
    if (values && editingProjectId.value) {
      await updateApiProjectApi(editingProjectId.value, values);
      message.success('项目更新成功');
      onRefresh();
      return true;
    }
    return false;
  },
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      projectFormApi.resetForm();
      editingProjectId.value = null;
    }
  },
});

// 操作处理
function onActionClick({ code, row }: OnActionClickParams<ApiProject>) {
  switch (code) {
    case 'delete': {
      deleteApiProjectApi(row.id).then(() => {
        message.success('项目删除成功');
        onRefresh();
      });
      break;
    }
    case 'edit': {
      editingProjectId.value = row.id;
      projectFormApi.setValues(row);
      editModalApi.open();
      break;
    }
  }
}

// 刷新表格
function onRefresh() {
  gridApi.query();
}

// 创建项目
function handleCreate() {
  createModalApi.open();
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <VbenButton @click="handleCreate">
          <MaterialSymbolsAdd class="size-5" />
          创建项目
        </VbenButton>
      </template>
    </Grid>

    <!-- 创建项目模态框 -->
    <CreateModal>
      <ProjectForm />
    </CreateModal>

    <!-- 编辑项目模态框 -->
    <EditModal>
      <ProjectForm />
    </EditModal>
  </Page>
</template>
