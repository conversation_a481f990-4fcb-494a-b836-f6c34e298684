#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Sequence

from sqlalchemy import Select

from backend.app.{{ app_name }}.crud.crud_{{ table_name }} import {{ table_name }}_dao
from backend.app.{{ app_name }}.model import {{ class_name }}
from backend.app.{{ app_name }}.schema.{{ table_name }} import Create{{ schema_name }}Param, Delete{{ schema_name }}Param, Update{{ schema_name }}Param
from backend.common.exception import errors
from backend.database.db import async_db_session


class {{ class_name }}Service:
    @staticmethod
    async def get(*, pk: int) -> {{ class_name }}:
        """
        获取{{ doc_comment }}

        :param pk: {{ doc_comment }} ID
        :return:
        """
        async with async_db_session() as db:
            {{ table_name }} = await {{ table_name }}_dao.get(db, pk)
            if not {{ table_name }}:
                raise errors.NotFoundError(msg='{{ doc_comment }}不存在')
            return {{ table_name }}

    @staticmethod
    async def get_select() -> Select:
        """获取{{ doc_comment }}查询对象"""
        return await {{ table_name }}_dao.get_list()

    @staticmethod
    async def get_all() -> Sequence[{{ class_name }}]:
        """获取所有{{ doc_comment }}"""
        async with async_db_session() as db:
            {{ table_name }}s = await {{ table_name }}_dao.get_all(db)
            return {{ table_name }}s

    @staticmethod
    async def create(*, obj: Create{{ schema_name }}Param) -> None:
        """
        创建{{ doc_comment }}

        :param obj: 创建{{ doc_comment }}参数
        :return:
        """
        async with async_db_session.begin() as db:
            await {{ table_name }}_dao.create(db, obj)

    @staticmethod
    async def update(*, pk: int, obj: Update{{ schema_name }}Param) -> int:
        """
        更新{{ doc_comment }}

        :param pk: {{ doc_comment }} ID
        :param obj: 更新{{ doc_comment }}参数
        :return:
        """
        async with async_db_session.begin() as db:
            count = await {{ table_name }}_dao.update(db, pk, obj)
            return count

    @staticmethod
    async def delete(*, obj: Delete{{ schema_name }}Param) -> int:
        """
        删除{{ doc_comment }}

        :param obj: {{ doc_comment }} ID 列表
        :return:
        """
        async with async_db_session.begin() as db:
            count = await {{ table_name }}_dao.delete(db, obj.pks)
            return count


{{ instance_name }}_service: {{ class_name }}Service = {{ class_name }}Service()
