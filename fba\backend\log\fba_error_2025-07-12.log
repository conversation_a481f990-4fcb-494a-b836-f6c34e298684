2025-07-10 11:24:11.774 | ERROR    | dec73823c4794e139c90c3caa872d15a | 请求异常: Git 仓库地址格式非法
2025-07-10 11:26:19.701 | ERROR    | b1c4846aeb6d41f79a704432bb364c28 | 请求异常: 插件不存在
2025-07-10 11:26:47.822 | ERROR    | b0c13797b89a4577970c0968f1440671 | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 11:26:58.497 | ERROR    | e565da1af0444612b0dd071abe3c41bc | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 11:27:08.712 | ERROR    | 7a2eeaaac94a460f9d0c552eef860a10 | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 11:30:21.494 | ERROR    | 236810e803ac4888baae2ee355da3b2b | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 11:30:47.468 | ERROR    | 8fcb5124c42c43a3a763da28fe29650c | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 11:31:56.495 | ERROR    | d23d4476d89b45e3b7d9289f25035c2b | 请求异常: fba_plugin_sms 插件已安装
2025-07-10 15:03:28.010 | ERROR    | be683e1f0d8c43a984e999e9e2c6717d | 腾讯云短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSdkAppId. Please check whether SmsSdkAppId is under the account associated with the TencentCloud API key requestId:b8710d82-88e0-454c-9350-f6ec941a4840
2025-07-10 15:03:28.013 | ERROR    | be683e1f0d8c43a984e999e9e2c6717d | 请求异常: 短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSdkAppId. Please check whether SmsSdkAppId is under the account associated with the TencentCloud API key requestId:b8710d82-88e0-454c-9350-f6ec941a4840
2025-07-10 15:03:28.070 | ERROR    | be683e1f0d8c43a984e999e9e2c6717d | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 584
               │     └ 3
               └ <function _main at 0x000001B65FD79630>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 584
           │    └ <function BaseProcess._bootstrap at 0x000001B65FCD1990>
           └ <SpawnProcess name='SpawnProcess-1' parent=28592 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001B65FCD1000>
    └ <SpawnProcess name='SpawnProcess-1' parent=28592 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001B65FD6A470>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=28592 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=28592 started>
    │    └ <function subprocess_started at 0x000001B6605431C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=28592 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 78, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001B65FD6A590>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 62, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001B660523D00>
           │       │   └ <uvicorn.server.Server object at 0x000001B65FD6A590>
           │       └ <function run at 0x000001B65FD7A680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001B6605FFBC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001B65FF06050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001B65FF05FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001B65FF07AC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001B65FE6F490>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 412, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001B660519510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 69, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B668...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001B6607FFB80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001B660519510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B668...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B668...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001B6685AA9B0>
          └ <fastapi.applications.FastAPI object at 0x000001B6607FFB80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001B66823B010>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001B6685AA9B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001B668682050>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001B...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000001B661F760E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001B668088780>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.S...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001B6684F3ED0>
    └ <contextlib._GeneratorContextManager object at 0x000001B668636E00>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B668680E50>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001B668635D50>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001B668635D50>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B668680E50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSd...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001B668682B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668683F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001B6685A9E70>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000001B6685AA0E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': '127.0.0.1:8000', 'connection': 'keep-alive', 'content-length': '28', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001B668682B00>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668683F40>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001B663234E50>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001B6685A9E70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001B6685A9E70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668683F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001B6685AA020>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001B6685A9E70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001B6685A9E70...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668683F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001B6685A9EA0>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000001B6685AA020>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000001B661F760E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001B6685AF500>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.S...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001B66861EDC0>
    └ <contextlib._GeneratorContextManager object at 0x000001B6687D3880>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B668682560>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001B6687D3340>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000001B6685A9EA0>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001B6685A9EA0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001B6687D3340>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B668682560>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSd...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001B668680550>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668681FC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001B668545A50>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000001B6685A9EA0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000001B661F760E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000001B6688E7A80>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.S...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000001B66861E9D0>
    └ <contextlib._GeneratorContextManager object at 0x000001B6687D3D90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B6686805E0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000001B6687D3C70>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001B6...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001B668545A50>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSd...

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000001B6687D3C70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000001B6686805E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ Exception('短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSd...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001B668681C60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001B668545B70>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000001B668545A50>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000001B668681C60>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001B6688135E0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001B6641BFD60>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001B668545B70>
          └ <function wrap_app_handling_exceptions at 0x000001B6631CADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668681630>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001B6641BFD60>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668681630>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001B6641BFD60>>
          └ <fastapi.routing.APIRouter object at 0x000001B6641BFD60>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668681630>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001B6630C0310>
          └ APIRoute(path='/api/v1/sms/send_login_code', name='send_login_code', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668681630>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001B6682275B0>
          └ APIRoute(path='/api/v1/sms/send_login_code', name='send_login_code', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668681630>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001B669A8D8A0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001B668682170>
          └ <function wrap_app_handling_exceptions at 0x000001B6631CADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001B668682710>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000001B668682200>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001B668682170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x000001B669A8D8A0>
                     └ <function get_request_handler.<locals>.app at 0x000001B668227BE0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001B6630C11B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'phone': '***********'}
                 │         └ <function send_login_code at 0x000001B668227E20>
                 └ <fastapi.dependencies.models.Dependant object at 0x000001B66857CC10>

  File "F:\gitpush\fastapi_best_architecture\backend\plugin\sms\api\v1\sms.py", line 57, in send_login_code
    await sms_service.send_sms(
          │           └ <function SmsService.send_sms at 0x000001B66813D510>
          └ <backend.plugin.sms.service.sms_service.SmsService object at 0x000001B6681920B0>

  File "F:\gitpush\fastapi_best_architecture\backend\plugin\sms\service\sms_service.py", line 103, in send_sms
    raise Exception(f"短信发送失败: {err}")

Exception: 短信发送失败: [TencentCloudSDKException] code:UnauthorizedOperation.SmsSdkAppIdVerifyFail message:failed to verify SmsSdkAppId. Please check whether SmsSdkAppId is under the account associated with the TencentCloud API key requestId:b8710d82-88e0-454c-9350-f6ec941a4840
2025-07-10 16:32:58.152 | ERROR    | 9e1064aa6d36410cbc3285eca01628a4 | 请求异常: 请求参数非法: username 字段为必填项，输入：{'phoneNumber': '***********', 'code': '123455', 'mode': 'mobile'}
2025-07-10 16:34:52.301 | ERROR    | 05abe8b26f194078994eb5e37550bf3b | 请求异常: 请求参数非法: password 字段为必填项，输入：{'username': '***********', 'code': '123444', 'mode': 'mobile'}
2025-07-10 17:42:57.558 | ERROR    | 94b80767bd034f949aeab46c1797364f | 短信登录错误: 该手机号未注册
2025-07-10 17:42:57.561 | ERROR    | 94b80767bd034f949aeab46c1797364f | 请求异常: 该手机号未注册
2025-07-10 17:44:16.942 | ERROR    | b0e60ac5d58546eaaa8e844b30e3aec0 | 短信登录错误: 验证码失效，请重新获取
2025-07-10 17:44:16.945 | ERROR    | b0e60ac5d58546eaaa8e844b30e3aec0 | 请求异常: 验证码失效，请重新获取
