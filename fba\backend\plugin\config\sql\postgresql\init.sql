insert into sys_config (id, name, type, key, value, is_frontend, remark, created_time, updated_time)
values
(1, '状态', 'EMAIL', 'EMAIL_STATUS', '1', 0, null, '2025-08-17 23:08:44', '2025-08-19 10:53:24'),
(2, '服务器地址', 'EMAIL', 'EMAIL_HOST', 'smtp.qq.com', 0, null, '2025-08-17 23:08:57', '2025-08-19 10:53:24'),
(3, '服务器端口', 'EMAIL', 'EMAIL_PORT', '465', 0, null, '2025-08-17 23:10:53', '2025-08-19 10:53:24'),
(4, '邮箱账号', 'EMAIL', 'EMAIL_USERNAME', '<EMAIL>', 0, null, '2025-08-17 23:11:47', '2025-08-19 10:53:24'),
(5, '邮箱密码', 'EMAIL', 'EMAIL_PASSWORD', '', 0, null, '2025-08-17 23:12:09', '2025-08-19 10:53:24'),
(6, 'SSL 加密', 'EMAIL', 'EMAIL_SSL', '1', 0, null, '2025-08-17 23:52:46', '2025-08-19 10:53:24');

-- reset auto-increment values for each table based on max id
select setval(pg_get_serial_sequence('sys_config', 'id'),coalesce(max(id), 0) + 1, true) from sys_config;
