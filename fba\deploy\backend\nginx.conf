server {
    listen       80 default_server;
    listen       [::]:80 default_server;
    server_name  127.0.0.1;

    root /fba;

    client_max_body_size 5M;
    client_body_buffer_size 5M;

    gzip on;
    gzip_comp_level 2;
    gzip_types text/plain text/css text/javascript application/javascript application/x-javascript application/xml application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;

    keepalive_timeout 300;

    location / {
        proxy_pass http://fba_server:8001;

        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    location /flower/ {
        proxy_pass http://fba_server:8555;

        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        proxy_redirect off;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /static {
        alias /var/www/fba_server/backend/static;
    }

    location /static/upload {
        alias /var/www/fba_server/backend/static/upload;
    }
}
