services:
  fba_server:
    build:
      context: .
      dockerfile: Dockerfile
    image: fba_server:latest
    container_name: fba_server
    restart: always
    depends_on:
      - fba_mysql
      - fba_redis
      - fba_celery
    volumes:
      - ./deploy/backend/docker-compose/.env.server:/fba/backend/.env
      - fba_static:/fba/backend/app/static
      - fba_static_upload:/fba/backend/static/upload
    networks:
      - fba_network
    command:
      - bash
      - -c
      - |
        wait-for-it -s fba_mysql:3306 -s fba_redis:6379 -t 300
        supervisord -c /etc/supervisor/supervisord.conf
        supervisorctl restart

  fba_mysql:
    image: mysql:8.0.41
    ports:
      - "${DOCKER_MYSQL_MAP_PORT:-3306}:3306"
    container_name: fba_mysql
    restart: always
    environment:
      MYSQL_DATABASE: fba
      MYSQL_ROOT_PASSWORD: 123456
      TZ: Asia/Shanghai
    volumes:
      - fba_mysql:/var/lib/mysql
    networks:
      - fba_network
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1

#  fba_postgres:
#    image: postgres:16
#    ports:
#      - "${DOCKER_POSTGRES_MAP_PORT:-5432}:5432"
#    container_name: fba_postgres
#    restart: always
#    environment:
#      POSTGRES_DB: fba
#      POSTGRES_PASSWORD: 123456
#      TZ: Asia/Shanghai
#    volumes:
#      - fba_postgres:/var/lib/postgresql/data
#    networks:
#      - fba_network

  fba_redis:
    image: redis
    ports:
      - "${DOCKER_REDIS_MAP_PORT:-6379}:6379"
    container_name: fba_redis
    restart: always
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - fba_redis:/data
    networks:
      - fba_network

  fba_nginx:
    image: nginx
    ports:
      - "8000:80"
    container_name: fba_nginx
    restart: always
    depends_on:
      - fba_server
    volumes:
      - ./deploy/backend/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - fba_static:/www/fba_server/backend/static
      - fba_static_upload:/www/fba_server/backend/static/upload
    networks:
      - fba_network

#  fba_ui:
#    build:
#      context: /root/fastapi_best_architecture_ui
#      dockerfile: Dockerfile
#    image: fba_ui:latest
#    ports:
#      - "80:80"
#      - "443:443"
#    container_name: fba_ui
#    restart: always
#    depends_on:
#      - fba_server
#    command:
#      - nginx
#      - -g
#      - daemon off;
#    volumes:
#      # - local_ssl_pem_path:/etc/ssl/xxx.pem
#      # - local_ssl_key_path:/etc/ssl/xxx.key
#      - fba_static:/www/fba_server/backend/static
#    networks:
#      - fba_network

  fba_rabbitmq:
    hostname: fba_rabbitmq
    image: rabbitmq:3.13.7
    ports:
      - "15672:15672"
      - "5672:5672"
    container_name: fba_rabbitmq
    restart: always
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - fba_rabbitmq:/var/lib/rabbitmq
    networks:
      - fba_network

  fba_celery:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - SERVER_TYPE=celery
    image: fba_celery:latest
    ports:
      - "8555:8555"
    container_name: fba_celery
    restart: always
    depends_on:
      - fba_rabbitmq
    volumes:
      - ./deploy/backend/docker-compose/.env.server:/fba/backend/.env
    networks:
      - fba_network
    command:
      - bash
      - -c
      - |
        wait-for-it -s fba_rabbitmq:5672 -t 300
        supervisord -c /etc/supervisor/supervisord.conf
        supervisorctl restart

networks:
  fba_network:
    name: fba_network
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ***********/24

volumes:
  fba_mysql:
    name: fba_mysql
  fba_redis:
    name: fba_redis
  fba_static:
    name: fba_static
  fba_static_upload:
    name: fba_static_upload
  fba_rabbitmq:
    name: fba_rabbitmq
