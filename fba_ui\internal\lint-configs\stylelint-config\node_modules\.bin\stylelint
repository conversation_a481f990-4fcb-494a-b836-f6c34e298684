#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules/stylelint/bin/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules/stylelint/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules/stylelint/bin/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules/stylelint/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/stylelint@16.23.1_typescript@5.9.2/node_modules:/mnt/f/gitpush/fastapi_best_architecture_ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
else
  exec node  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
fi
