2025-07-12 10:50:23.453 | ERROR    | fd5e2e50e4b043299474d137268c538e | 请求异常: 验证码错误
2025-07-12 11:05:04.041 | ERROR    | 53c990b02df64eb193765bcec548b0ab | 短信登录错误: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:05:04.044 | ERROR    | 53c990b02df64eb193765bcec548b0ab | 请求异常: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:05:04.059 | ERROR    | 53c990b02df64eb193765bcec548b0ab | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x000002822C949630>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x000002822C8A1990>
           └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002822C8A1000>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002822C93A470>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    └ <function subprocess_started at 0x000002822D1131C0>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 78, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002822C93A590>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 62, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002822D0F3D00>
           │       │   └ <uvicorn.server.Server object at 0x000002822C93A590>
           │       └ <function run at 0x000002822C94A680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002822D1CFBC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002822CADA050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002822CAD9FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002822CADBAC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002822CA3F490>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 412, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 69, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>
          └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002823553C430>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x000002823553DA20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028235603840>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000028235498580>
    └ <contextlib._GeneratorContextManager object at 0x0000028235555510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002823553DE10>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000028235557310>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000028235557310>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002823553DE10>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002823553CAF0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002823553DCF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '39', 'sec-ch-ua-platform': '"Windows"', 'ac...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002823553CAF0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002823553DCF0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002822FE04E50>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002823553DCF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002823553DCF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028235600E80>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F2180>
    └ <contextlib._GeneratorContextManager object at 0x0000028235539780>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002823553DAB0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002823553B970>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002823553B970>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002823553DAB0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002823553DB40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002823553DBD0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028235603900>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F3A00>
    └ <contextlib._GeneratorContextManager object at 0x00000282355199C0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282355CA320>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000028235519000>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000282...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000028235519000>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282355CA320>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282355C8E50>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282355C8E50>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002823551B130>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA950>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA950>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000028230D8FD30>>
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA950>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002822EBD0310>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA950>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000028234FB7010>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA950>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002823551BE50>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000282355CAA70>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282355CA9E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CA830>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000282355CAA70>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x000002823551BE50>
                     └ <function get_request_handler.<locals>.app at 0x0000028234FB6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002822EBD11B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'obj': SmsLoginParam(phone='***********', code='123456'), 'request': <starlette.requests.Request object at 0x000002823551BE5...
                 │         └ <function login_by_sms at 0x0000028234DE43A0>
                 └ <fastapi.dependencies.models.Dependant object at 0x0000028235149FC0>

  File "F:\gitpush\fastapi_best_architecture\backend\plugin\sms\api\v1\sms.py", line 74, in login_by_sms
    data = await auth_service.login_by_sms(
                 │            └ <function AuthService.login_by_sms at 0x00000282331DDBD0>
                 └ <backend.app.admin.service.auth_service.AuthService object at 0x000002823320B670>

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 245, in login_by_sms
    raise e

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 185, in login_by_sms
    raise errors.CustomError(error=CustomErrorCode.CAPTCHA_ERROR, msg='验证码错误')
          │      │                 │               └ <CustomErrorCode.CAPTCHA_ERROR: (40001, '验证码错误')>
          │      │                 └ <enum 'CustomErrorCode'>
          │      └ <class 'backend.common.exception.errors.CustomError'>
          └ <module 'backend.common.exception.errors' from 'F:\\gitpush\\fastapi_best_architecture\\backend\\common\\exception\\errors.py'>

TypeError: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:12:22.287 | ERROR    | 8a49c49386544cde95cb8b7e70de364c | 短信登录错误: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:12:22.288 | ERROR    | 8a49c49386544cde95cb8b7e70de364c | 请求异常: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:12:22.306 | ERROR    | 8a49c49386544cde95cb8b7e70de364c | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x000002822C949630>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x000002822C8A1990>
           └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002822C8A1000>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002822C93A470>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    └ <function subprocess_started at 0x000002822D1131C0>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 78, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002822C93A590>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 62, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002822D0F3D00>
           │       │   └ <uvicorn.server.Server object at 0x000002822C93A590>
           │       └ <function run at 0x000002822C94A680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002822D1CFBC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002822CADA050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002822CAD9FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002822CADBAC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002822CA3F490>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 412, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 69, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>
          └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000282355CAF80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000282355C8CA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028236757FC0>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F1EE0>
    └ <contextlib._GeneratorContextManager object at 0x000002823551B8B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282355CA7A0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002823551BB50>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002823551BB50>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282355CA7A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282355CACB0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CB0A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '39', 'sec-ch-ua-platform': '"Windows"', 'ac...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282355CACB0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CB0A0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002822FE04E50>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CB0A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282355CB0A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028236755580>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F2E30>
    └ <contextlib._GeneratorContextManager object at 0x0000028235518CA0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742200>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000028235518C70>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000028235518C70>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742200>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236742320>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742290>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028236754A80>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002823674D380>
    └ <contextlib._GeneratorContextManager object at 0x00000282355B70A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282367425F0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x00000282355B79A0>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000282...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x00000282355B79A0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000282367425F0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282367427A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000282367427A0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000282355B6950>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000028230D8FD30>>
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742B00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002822EBD0310>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000028234FB7010>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742B00>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000282355B6320>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000028236742B90>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236742C20>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742950>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000028236742B90>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x00000282355B6320>
                     └ <function get_request_handler.<locals>.app at 0x0000028234FB6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002822EBD11B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'obj': SmsLoginParam(phone='***********', code='111111'), 'request': <starlette.requests.Request object at 0x00000282355B632...
                 │         └ <function login_by_sms at 0x0000028234DE43A0>
                 └ <fastapi.dependencies.models.Dependant object at 0x0000028235149FC0>

  File "F:\gitpush\fastapi_best_architecture\backend\plugin\sms\api\v1\sms.py", line 74, in login_by_sms
    data = await auth_service.login_by_sms(
                 │            └ <function AuthService.login_by_sms at 0x00000282331DDBD0>
                 └ <backend.app.admin.service.auth_service.AuthService object at 0x000002823320B670>

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 245, in login_by_sms
    raise e

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 185, in login_by_sms
    raise errors.CustomError(error=CustomErrorCode.CAPTCHA_ERROR, msg='验证码错误')
          │      │                 │               └ <CustomErrorCode.CAPTCHA_ERROR: (40001, '验证码错误')>
          │      │                 └ <enum 'CustomErrorCode'>
          │      └ <class 'backend.common.exception.errors.CustomError'>
          └ <module 'backend.common.exception.errors' from 'F:\\gitpush\\fastapi_best_architecture\\backend\\common\\exception\\errors.py'>

TypeError: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:15:24.044 | ERROR    | bdde1c26cf2244708ecf23598319c02b | 短信登录错误: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:15:24.045 | ERROR    | bdde1c26cf2244708ecf23598319c02b | 请求异常: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:15:24.048 | ERROR    | bdde1c26cf2244708ecf23598319c02b | Exception in ASGI application

Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x000002822C949630>

  File "D:\Python310\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x000002822C8A1990>
           └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002822C8A1000>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "D:\Python310\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002822C93A470>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>
    │    └ <function subprocess_started at 0x000002822D1131C0>
    └ <SpawnProcess name='SpawnProcess-2' parent=19052 started>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\_subprocess.py", line 78, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002822C93A590>>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\server.py", line 62, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=324, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002822D0F3D00>
           │       │   └ <uvicorn.server.Server object at 0x000002822C93A590>
           │       └ <function run at 0x000002822C94A680>
           └ <module 'asyncio' from 'D:\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002822D1CFBC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002822CADA050>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002822CAD9FC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002822CADBAC0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "D:\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002822CA3F490>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

  File "D:\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future cancelled>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future cancelled>)>

> File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 412, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 69, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002822D0E9510>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028235...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>
          └ <fastapi.applications.FastAPI object at 0x000002822D3CFB80>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000028236742E60>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002823516C460>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000028236742CB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>, header_name...

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x000002823676CC00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F2490>
    └ <contextlib._GeneratorContextManager object at 0x00000282355B6170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742680>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x00000282355B6770>
                     │    └ <bound method AccessMiddleware.dispatch of <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>>
                     └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\access_middleware.py", line 34, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x00000282355B6770>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742680>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236742F80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742EF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x000002823516E380>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '39', 'sec-ch-ua-platform': '"Windows"', 'ac...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236742F80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742EF0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002822FE04E50>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742EF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002823516E1A0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742EF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x000002823516E170>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028236755F00>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000282351F2F80>
    └ <contextlib._GeneratorContextManager object at 0x00000282355B7970>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742560>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x00000282355B7910>
                     │    └ <bound method StateMiddleware.dispatch of <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>>
                     └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\state_middleware.py", line 32, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x00000282355B7910>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236742560>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236743130>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028236742710>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x000002823516E230>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002822FC220E0>

  File "D:\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
    │    │   │     │    │      └ <traceback object at 0x0000028236760F80>
    │    │   │     │    └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'...
    │    │   │     └ <class 'exceptiongroup.ExceptionGroup'>
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002823674D620>
    └ <contextlib._GeneratorContextManager object at 0x0000028236768190>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_utils.py", line 93, in collapse_excgroups
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236743370>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000028236768130>
                     │    └ <bound method OperaLogMiddleware.dispatch of <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000282...
                     └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 115, in dispatch
    raise error from None
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\backend\middleware\opera_log_middleware.py", line 51, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000028236768130>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000028236743370>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ TypeError("CustomError.__init__() got an unexpected keyword argument 'msg'")

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236743520>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x000002823516E0E0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028236743520>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000028236768520>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002823516E1D0>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236743880>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236743880>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000028230D8FD30>>
          └ <fastapi.routing.APIRouter object at 0x0000028230D8FD30>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236743880>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002822EBD0310>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236743880>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000028234FB7010>
          └ APIRoute(path='/api/v1/sms/login/sms', name='login_by_sms', methods=['POST'])

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028236743880>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000028236768A60>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000028236743910>
          └ <function wrap_app_handling_exceptions at 0x000002822FD9ADD0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000282367439A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000282367436D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000028236743910>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x0000028236768A60>
                     └ <function get_request_handler.<locals>.app at 0x0000028234FB6830>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002822EBD11B0>

  File "F:\gitpush\fastapi_best_architecture\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'obj': SmsLoginParam(phone='***********', code='123456'), 'request': <starlette.requests.Request object at 0x0000028236768A6...
                 │         └ <function login_by_sms at 0x0000028234DE43A0>
                 └ <fastapi.dependencies.models.Dependant object at 0x0000028235149FC0>

  File "F:\gitpush\fastapi_best_architecture\backend\plugin\sms\api\v1\sms.py", line 74, in login_by_sms
    data = await auth_service.login_by_sms(
                 │            └ <function AuthService.login_by_sms at 0x00000282331DDBD0>
                 └ <backend.app.admin.service.auth_service.AuthService object at 0x000002823320B670>

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 245, in login_by_sms
    raise e

  File "F:\gitpush\fastapi_best_architecture\backend\app\admin\service\auth_service.py", line 185, in login_by_sms
    raise errors.CustomError(error=CustomErrorCode.CAPTCHA_ERROR, msg='验证码错误')
          │      │                 │               └ <CustomErrorCode.CAPTCHA_ERROR: (40001, '验证码错误')>
          │      │                 └ <enum 'CustomErrorCode'>
          │      └ <class 'backend.common.exception.errors.CustomError'>
          └ <module 'backend.common.exception.errors' from 'F:\\gitpush\\fastapi_best_architecture\\backend\\common\\exception\\errors.py'>

TypeError: CustomError.__init__() got an unexpected keyword argument 'msg'
2025-07-12 11:35:31.657 | ERROR    | 39c828ecd75047e692a6ecb4f03b7ad6 | 请求异常: 请求参数非法: username 字段为必填项，输入：{'code': '123456'}
2025-07-12 11:36:11.907 | ERROR    | 2b6d74cdc22f4f008cea15d45e5001fb | 请求异常: 请求参数非法: username 字段为必填项，输入：{'code': '123456'}
