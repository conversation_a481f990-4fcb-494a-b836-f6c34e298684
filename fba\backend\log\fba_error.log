2025-09-01 15:50:02.999 | ERROR    | 595b572a8d7c4a13816ecc4e018668f4 | 请求异常: Not Authenticated
2025-09-01 15:50:14.840 | ERROR    | ce8265ccd4e44198aa7758617a74dfb0 | 请求异常: Not Authenticated
2025-09-01 15:50:35.516 | ERROR    | 0cf14e90f29e439eb0a40fbbee6bfa1a | 请求异常: Not Authenticated
2025-09-01 15:51:32.937 | ERROR    | d489e9c029cc4bdb9f395625cd9473c5 | 请求异常: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`fba`.`sys_dept`, CONSTRAINT `sys_dept_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `sys_dept` (`id`) ON DELETE SET NULL)')
[SQL: INSERT INTO sys_dept (name, sort, leader, phone, email, status, del_flag, parent_id, created_time, updated_time) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: ('string', 0, 'string', '14620729560', '<EMAIL>', 0, 0, 0, datetime.datetime(2025, 9, 1, 15, 51, 32, 929506, tzinfo=zoneinfo.ZoneInfo(key='Asia/Shanghai')), None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-01 15:51:32.938 | ERROR    | d489e9c029cc4bdb9f395625cd9473c5 | Exception in ASGI application

2025-09-01 15:51:45.812 | ERROR    | 17e2c83d5c3044299c57957404b78300 | 请求异常: 父级部门不存在
2025-09-01 15:51:50.981 | ERROR    | 100c156fdf264684952d794677c437ed | 请求异常: 父级部门不存在
2025-09-01 15:51:56.784 | ERROR    | 2a7ab42ed9734b5abc3e9295c241118c | 请求异常: 父级部门不存在
2025-09-01 15:52:22.664 | ERROR    | 252b1db6cf8143d9a4913000d1d726e9 | 请求异常: 字典类型不存在
2025-09-01 16:00:05.266 | ERROR    | bf4f4a73a9d04b22b8739fe009484cfd | 请求异常: 请求参数非法: limit 输入应为有效的整数，无法将字符串解析为整数，输入：
