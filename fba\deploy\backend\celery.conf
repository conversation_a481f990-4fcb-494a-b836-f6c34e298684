[program:celery_worker]
directory=/fba/backend
command=/usr/local/bin/celery -A app.task.celery worker -P gevent -c 1000 --loglevel=INFO
user=root
autostart=true
autorestart=true
startretries=5
redirect_stderr=true
stdout_logfile=/var/log/celery/fba_celery_worker.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5

[program:celery_beat]
directory=/fba/backend
command=/usr/local/bin/celery -A app.task.celery beat --loglevel=INFO
user=root
autostart=true
autorestart=true
startretries=5
redirect_stderr=true
stdout_logfile=/var/log/celery/fba_celery_beat.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5

[program:celery_flower]
directory=/fba/backend
command=/usr/local/bin/celery -A app.task.celery flower --port=8555 --url-prefix=flower --basic-auth=admin:123456
user=root
autostart=true
autorestart=true
startretries=5
redirect_stderr=true
stdout_logfile=/var/log/celery/fba_celery_flower.log
stdout_logfile_maxbytes=5MB
stdout_logfile_backups=5
