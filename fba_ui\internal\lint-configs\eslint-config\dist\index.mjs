import { createJiti } from "../../../../node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/lib/jiti.mjs";

const jiti = createJiti(import.meta.url, {
  "interopDefault": true,
  "alias": {
    "@vben/eslint-config": "F:/gitpush/fastapi_best_architecture_ui/internal/lint-configs/eslint-config"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("F:/gitpush/fastapi_best_architecture_ui/internal/lint-configs/eslint-config/src/index.js")} */
const _module = await jiti.import("F:/gitpush/fastapi_best_architecture_ui/internal/lint-configs/eslint-config/src/index.ts");

export const defineConfig = _module.defineConfig;