#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from uuid import UUID

import sqlalchemy as sa

{% if database_type == 'mysql' -%}
from sqlalchemy.dialects import mysql
{% else -%}
from sqlalchemy.dialects import postgresql
{% endif -%}
from sqlalchemy.orm import Mapped, mapped_column

from backend.common.model import {% if default_datetime_column %}Base{% else %}MappedBase{% endif %}, id_key


class {{ class_name }}({% if default_datetime_column %}Base{% else %}MappedBase{% endif %}):
    """{{ table_comment }}"""

    __tablename__ = '{{ table_name }}'

    id: Mapped[id_key] = mapped_column(init=False)
    {% for model in models %}
    {{ model.name }}:
    {%- if model.is_nullable %} Mapped[{{ model.pd_type }} | None]
    {%- else %} Mapped[{{ model.pd_type }}]
    {%- endif %} = mapped_column(
    {%- if model.type in ['NVARCHAR', 'String', 'Unicode', 'VARCHAR'] -%}
        sa.String({{ model.length }})
    {%- elif database_type == 'mysql' and model.type in ['BIT', 'ENUM', 'LONGBLOB', 'LONGTEXT', 'MEDIUMBLOB',
    'MEDIUMINT', 'MEDIUMTEXT', 'SET', 'TINYBLOB', 'TINYINT', 'TINYTEXT', 'YEAR'] -%}
        mysql.{{ model.type }}()
    {%- elif database_type == 'postgresql' and model.type in [
    'ARRAY', 'BIT', 'BYTEA', 'CIDR', 'CITEXT', 'DATEMULTIRANGE', 'DATERANGE', 'DOMAIN', 'ENUM', 'HSTORE', 'INET',
    'INT4MULTIRANGE', 'INT4RANGE', 'INT8MULTIRANGE', 'INT8RANGE', 'INTERVAL', 'JSONB', 'JSONPATH', 'MACADDR',
    'MACADDR8', 'MONEY', 'NUMMULTIRANGE', 'NUMRANGE', 'OID', 'REGCLASS', 'REGCONFIG', 'TSMULTIRANGE', 'TSQUERY',
    'TSRANGE', 'TSTZMULTIRANGE', 'TSTZRANGE', 'TSVECTOR'] -%}
    {%- else -%}
        sa.{{ model.type }}()
    {%- endif -%}, default=
    {%- if model.is_nullable and model.default == None -%}
        None
    {%- else -%}
        {%- if model.default != None -%}
            '{{ model.default }}'
        {%- else -%}
            {%- if model.pd_type == 'str' -%}
                ''
            {%- elif model.pd_type == 'int' -%}
                0
            {%- elif model.pd_type == 'bytes' -%}
                b''
            {%- elif model.pd_type == 'bool' -%}
                True
            {%- elif model.pd_type == 'float' -%}
                0.0
            {%- elif model.pd_type == 'dict' -%}
                {}
            {%- elif model.pd_type == 'date' or model.pd_type == 'datetime' -%}
                timezone.now()
            {%- elif model.pd_type == 'list[str]' -%}
                ()
            {%- else -%}
                ''
            {%- endif -%}
        {%- endif -%}
    {%- endif -%}{% if model.sort != 0 %}, sort_order={{ model.sort }}{% endif %}, comment=
    {%- if model.comment != None -%}
        '{{ model.comment }}')
    {% else -%}
        None)
    {%- endif -%}
    {% endfor %}
