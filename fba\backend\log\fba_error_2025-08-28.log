2025-08-26 17:36:25.855 | ERROR    | b228d029a326406692a1bd00d50d211d | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'name': '乌雅桂兰', 'query': 'SELECT * FROM api_project;', 'success': True, 'error': None, 'data': [], 'affected_rows': 0, 'extracted_variables': None})}

2025-08-26 17:36:25.857 | ERROR    | b228d029a326406692a1bd00d50d211d | Exception in ASGI application

2025-08-26 17:45:59.270 | ERROR    | 28d034b6a1a04a259604bfefcf61cbd7 | 请求异常: 请求参数非法: body 输入应为有效的列表，输入：{'name': '查询SQL', 'query': 'SELECT * FROM sys_dept;', 'extract': {}, 'validations': [], 'use_default_db': True, 'db_config': {'type': 'mysql', 'host': '127.0.0.1', 'port': 3306, 'username': 'root', 'password': '123456', 'database': 'fba'}}
2025-08-26 17:48:58.218 | ERROR    | cc27c8396d074294b4a8fb79a08d404c | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]' at line 1")
2025-08-26 17:48:58.219 | ERROR    | cc27c8396d074294b4a8fb79a08d404c | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'results': [{'name': '批量执行SQL', 'query': '[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]', 'success': False, 'error': 'SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]\' at line 1")', 'data': None, 'affected_rows': None, 'extracted_variables': None}], 'summary': {'total': 1, 'success': 0, 'failed': 1}})}

2025-08-26 17:48:58.221 | ERROR    | cc27c8396d074294b4a8fb79a08d404c | Exception in ASGI application

2025-08-26 17:49:06.575 | ERROR    | c5676fd8c1e9427bb16a301c92e267e8 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]' at line 1")
2025-08-26 17:49:06.576 | ERROR    | c5676fd8c1e9427bb16a301c92e267e8 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'results': [{'name': '批量执行SQL', 'query': '[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]', 'success': False, 'error': 'SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'[SELECT * FROM sys_dept;,SELECT * FROM sys_user;]\' at line 1")', 'data': None, 'affected_rows': None, 'extracted_variables': None}], 'summary': {'total': 1, 'success': 0, 'failed': 1}})}

2025-08-26 17:49:06.578 | ERROR    | c5676fd8c1e9427bb16a301c92e267e8 | Exception in ASGI application

2025-08-26 17:49:18.635 | ERROR    | 04147c921e7f48618e1b56417305085c | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=200, msg='请求成功', data={'results': [{'name': '批量执行SQL', 'query': 'SELECT * FROM sys_dept;', 'success': True, 'error': None, 'data': [{'id': 2048601258595581952, 'name': '测试', 'sort': 0, 'leader': None, 'phone': None, 'email': None, 'status': 1, 'del_flag': 0, 'parent_id': None, 'created_time': datetime.datetime(2025, 7, 16, 16, 55, 28), 'updated_time': None}], 'affected_rows': 1, 'extracted_variables': None}], 'summary': {'total': 1, 'success': 1, 'failed': 0}})}

2025-08-26 17:49:18.637 | ERROR    | 04147c921e7f48618e1b56417305085c | Exception in ASGI application

2025-08-26 17:50:23.780 | ERROR    | a53e9d6e1e824b4e9d4926714a7a5cb1 | 请求异常: 请求参数非法: body 输入应为有效的字典或可提取字段的对象，输入：[{'name': '批量执行SQL', 'query': 'SELECT * FROM sys_dept;', 'extract': {}, 'validations': [], 'use_default_db': True, 'db_config': {'type': 'mysql', 'host': '127.0.0.1', 'port': 3306, 'username': 'root', 'password': '123456', 'database': 'fba'}}]
2025-08-26 17:52:05.251 | ERROR    | fc4d4edeebc447f4984428a3ef3d5e83 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="批量SQL执行失败: 'success'")}

2025-08-26 17:52:05.253 | ERROR    | fc4d4edeebc447f4984428a3ef3d5e83 | Exception in ASGI application

2025-08-26 17:53:15.086 | ERROR    | bbfbd08745d44a168a906078de85d4a2 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept]' at line 1")
2025-08-26 17:53:15.087 | ERROR    | bbfbd08745d44a168a906078de85d4a2 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="批量SQL执行失败: 'success'")}

2025-08-26 17:53:15.090 | ERROR    | bbfbd08745d44a168a906078de85d4a2 | Exception in ASGI application

2025-08-26 17:53:21.808 | ERROR    | 5cca583ebc044c539bfb09f43e4b4d6a | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept]' at line 1")
2025-08-26 17:53:21.808 | ERROR    | 5cca583ebc044c539bfb09f43e4b4d6a | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="批量SQL执行失败: 'success'")}

2025-08-26 17:53:21.811 | ERROR    | 5cca583ebc044c539bfb09f43e4b4d6a | Exception in ASGI application

2025-08-26 17:54:33.878 | ERROR    | 1f9d0e34cf8c4990b85fbf73d8b3f125 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="批量SQL执行失败: 'success'")}

2025-08-26 17:54:33.879 | ERROR    | 1f9d0e34cf8c4990b85fbf73d8b3f125 | Exception in ASGI application

2025-08-26 17:55:35.006 | ERROR    | d17c5eb24c8140ffb4fb5bb189386829 | Exception in ASGI application

2025-08-26 17:55:44.260 | ERROR    | 76ecfa86fe074f2a904835a125559d91 | 请求异常: 1 validation errors:
  {'type': 'dict_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary', 'input': ResponseModel(code=400, msg='请求错误', data="批量SQL执行失败: 'success'")}

2025-08-26 17:55:44.262 | ERROR    | 76ecfa86fe074f2a904835a125559d91 | Exception in ASGI application

2025-08-26 18:01:23.332 | ERROR    | 8fab7429598e4ffebadaee43d605b7e6 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept,SELECT * FROM sys_user]' at line 1")
2025-08-26 18:01:57.001 | ERROR    | cd0677d59f9e4f0b9daea253b73c4681 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '[SELECT * FROM sys_dept],[SELECT * FROM sys_user]' at line 1")
2025-08-26 18:02:48.453 | ERROR    | ff7dd4cb9cd94335839cd44e31d1e512 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'SELECT * FROM sys_user' at line 1")
2025-08-26 18:03:15.359 | ERROR    | 5e61ec4e56e747239c85e9c2f39ef578 | SQL执行异常: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'SELECT * FROM sys_user; SELECT * FROM sys_role' at line 1")
